# Complete VPS Setup Script

This script provides a complete, automated setup for a production-ready VPS with advanced security hardening.

## Features

### 🔧 **Basic Infrastructure**
- Ubuntu 22.04 LTS system updates
- Nginx web server with security headers
- PHP 8.2 with all essential extensions
- PostgreSQL database server
- Composer for PHP package management
- Let's Encrypt SSL certificates

### 🛡️ **Advanced Security**
- SSH hardening with custom port
- Fail2Ban intrusion prevention
- UFW firewall configuration
- ClamAV antivirus scanning
- Kernel security hardening
- PHP security configuration
- R<PERSON><PERSON>unter & ChkRootkit for rootkit detection
- Lynis security auditing

### 📊 **Monitoring & Automation**
- Automated security scanning
- Intrusion detection system
- Email alerts for security events
- Automated system updates
- Log rotation and cleanup
- Security status dashboard

## Quick Start

### 1. Download and Run

```bash
# Download the script
wget https://raw.githubusercontent.com/your-repo/complete-vps-setup.sh

# Make it executable
chmod +x complete-vps-setup.sh

# Run the script as root
sudo ./complete-vps-setup.sh
```

### 2. Configuration

Before running, you can modify these variables in the script:

```bash
DOMAIN_NAME="tadacip.store"        # Your domain name
ADMIN_EMAIL="<EMAIL>"  # Admin email for notifications
SSH_PORT="2222"                    # Custom SSH port
DB_NAME="fasgo_db"                 # Database name
DB_USER="fasgo_user"               # Database user
```

### 3. Post-Setup Steps

After the script completes:

1. **Update Cloudflare DNS** to point to your server IP
2. **Configure SSH keys** before disconnecting
3. **Test website access** at your domain
4. **Review security status** with `/opt/fasgo-security/security-status.sh`

## What the Script Does

### Step-by-Step Process

1. **System Update** - Updates Ubuntu and installs essential packages
2. **Nginx Setup** - Installs and configures web server with security headers
3. **PHP Installation** - Installs PHP 8.2 with all necessary extensions
4. **PostgreSQL Setup** - Configures database server and creates user
5. **Composer Installation** - Installs PHP package manager
6. **SSL Configuration** - Sets up Let's Encrypt certificates
7. **Firewall Setup** - Configures UFW with restrictive rules
8. **SSH Hardening** - Secures SSH with custom port and strong crypto
9. **Fail2Ban Setup** - Configures intrusion prevention
10. **Security Tools** - Installs ClamAV, RKHunter, ChkRootkit, Lynis
11. **Kernel Hardening** - Applies security kernel parameters
12. **PHP Security** - Hardens PHP configuration
13. **Monitoring Setup** - Creates security monitoring scripts
14. **Automation** - Sets up cron jobs for automated tasks
15. **Website Structure** - Creates web directories and landing page
16. **Finalization** - Restarts services and generates reports

## Security Features

### 🔐 **SSH Security**
- Custom port (default: 2222)
- Strong encryption algorithms
- Failed attempt limiting
- Verbose logging
- Security banner

### 🛡️ **Intrusion Prevention**
- Fail2Ban with multiple jails
- Real-time monitoring
- Automatic IP blocking
- Email notifications

### 🦠 **Malware Protection**
- ClamAV antivirus scanning
- Daily security scans
- Automatic malware removal
- Virus database updates

### 🔍 **System Monitoring**
- Failed login tracking
- Resource usage monitoring
- Suspicious process detection
- Security event logging

## Generated Files

After setup completion, you'll find:

- `/root/setup_report.txt` - Complete setup summary
- `/root/database_credentials.txt` - Database login details
- `/opt/fasgo-security/` - Security monitoring scripts
- `/var/log/fasgo-security.log` - Security scan logs
- `/var/log/fasgo-intrusion.log` - Intrusion detection logs

## Useful Commands

### Security Status
```bash
/opt/fasgo-security/security-status.sh
```

### Manual Security Scan
```bash
/opt/fasgo-security/security-scan.sh
```

### Check Fail2Ban Status
```bash
fail2ban-client status
```

### View Firewall Rules
```bash
ufw status verbose
```

### Check Service Status
```bash
systemctl status nginx php8.2-fpm postgresql fail2ban
```

## Troubleshooting

### SSH Connection Issues
If you can't connect via SSH after setup:
1. Use the new port: `ssh -p 2222 root@your-server-ip`
2. Ensure SSH keys are properly configured
3. Check firewall rules: `ufw status`

### SSL Certificate Issues
If SSL certificates fail to install:
1. Ensure domain DNS points to server IP
2. Check domain accessibility via HTTP first
3. Verify Cloudflare proxy is disabled during setup

### Service Issues
If services fail to start:
1. Check service logs: `journalctl -u service-name`
2. Verify configuration files
3. Run configuration tests (e.g., `nginx -t`)

## Requirements

- Fresh Ubuntu 22.04 LTS VPS
- Root access
- Domain name pointing to server IP
- Minimum 1GB RAM, 1 CPU core, 20GB storage

## Support

For issues or questions:
1. Check the setup report: `/root/setup_report.txt`
2. Review security logs: `/var/log/fasgo-security.log`
3. Run security status: `/opt/fasgo-security/security-status.sh`

---

**⚠️ Important:** This script makes significant system changes. Only run on fresh VPS installations.
