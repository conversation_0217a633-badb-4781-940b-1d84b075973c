#!/bin/bash

# Complete VPS Setup Script - Fasgo Production Server
# This script sets up a complete production-ready VPS with advanced security
# Author: Fasgo Team
# Version: 1.0
# Date: 2025-06-18

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
DOMAIN_NAME="tadacip.store"
ADMIN_EMAIL="<EMAIL>"
SSH_PORT="2222"
DB_NAME="fasgo_db"
DB_USER="fasgo_user"
DB_PASSWORD=$(openssl rand -base64 32)

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
    fi
}

# Display banner
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    FASGO VPS COMPLETE SETUP SCRIPT"
    echo "=================================================="
    echo "Setting up production-ready VPS with:"
    echo "- Ubuntu 22.04 LTS Security Hardening"
    echo "- Nginx Web Server"
    echo "- PHP 8.2 with Security Configuration"
    echo "- PostgreSQL Database"
    echo "- SSL Certificates (Let's Encrypt)"
    echo "- Advanced Security Suite"
    echo "- Cloudflare Integration"
    echo "- Automated Monitoring"
    echo "=================================================="
    echo -e "${NC}"
}

# Step 1: System Update and Basic Setup
setup_system() {
    log "Step 1: Updating system and installing basic packages"
    
    # Update package lists
    apt update -y
    
    # Upgrade system
    apt upgrade -y
    
    # Install essential packages
    apt install -y curl wget git unzip software-properties-common \
                   apt-transport-https ca-certificates gnupg lsb-release \
                   htop nano vim tree
    
    # Set timezone
    timedatectl set-timezone Europe/Berlin
    
    log "System update completed"
}

# Step 2: Install and Configure Nginx
setup_nginx() {
    log "Step 2: Installing and configuring Nginx"
    
    apt install -y nginx
    
    # Enable and start Nginx
    systemctl enable nginx
    systemctl start nginx
    
    # Create basic security configuration
    cat > /etc/nginx/conf.d/security.conf << 'EOF'
# Security headers
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

# Hide Nginx version
server_tokens off;

# Gzip compression
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied expired no-cache no-store private auth;
gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/json;
EOF
    
    log "Nginx installation completed"
}

# Step 3: Install PHP 8.2
setup_php() {
    log "Step 3: Installing PHP 8.2 and extensions"
    
    # Add PHP repository
    add-apt-repository ppa:ondrej/php -y
    apt update -y
    
    # Install PHP and extensions
    apt install -y php8.2 php8.2-fpm php8.2-mysql php8.2-pgsql \
                   php8.2-mbstring php8.2-xml php8.2-curl php8.2-zip \
                   php8.2-gd php8.2-intl php8.2-bcmath php8.2-soap \
                   php8.2-imagick php8.2-redis
    
    # Enable and start PHP-FPM
    systemctl enable php8.2-fpm
    systemctl start php8.2-fpm
    
    log "PHP 8.2 installation completed"
}

# Step 4: Install PostgreSQL
setup_postgresql() {
    log "Step 4: Installing and configuring PostgreSQL"
    
    apt install -y postgresql postgresql-contrib
    
    # Enable and start PostgreSQL
    systemctl enable postgresql
    systemctl start postgresql
    
    # Create database and user
    sudo -u postgres psql << EOF
CREATE DATABASE ${DB_NAME};
CREATE USER ${DB_USER} WITH ENCRYPTED PASSWORD '${DB_PASSWORD}';
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};
ALTER USER ${DB_USER} CREATEDB;
\q
EOF
    
    # Save database credentials
    cat > /root/database_credentials.txt << EOF
Database Name: ${DB_NAME}
Database User: ${DB_USER}
Database Password: ${DB_PASSWORD}
EOF
    
    chmod 600 /root/database_credentials.txt
    
    log "PostgreSQL installation completed"
}

# Step 5: Install Composer
setup_composer() {
    log "Step 5: Installing Composer"
    
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer
    
    log "Composer installation completed"
}

# Step 6: Setup SSL with Certbot
setup_ssl() {
    log "Step 6: Installing SSL certificates"
    
    # Install Certbot
    apt install -y certbot python3-certbot-nginx
    
    # Create Nginx virtual host for domain
    cat > /etc/nginx/sites-available/${DOMAIN_NAME} << EOF
server {
    listen 80;
    listen [::]:80;
    
    server_name ${DOMAIN_NAME} www.${DOMAIN_NAME};
    root /var/www/html;
    index index.php index.html index.htm;
    
    location / {
        try_files \$uri \$uri/ =404;
    }
    
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
EOF
    
    # Enable site
    ln -sf /etc/nginx/sites-available/${DOMAIN_NAME} /etc/nginx/sites-enabled/
    
    # Test Nginx configuration
    nginx -t && systemctl reload nginx
    
    # Get SSL certificate
    certbot --nginx -d ${DOMAIN_NAME} -d www.${DOMAIN_NAME} \
            --non-interactive --agree-tos --email ${ADMIN_EMAIL}
    
    log "SSL certificates installed"
}

# Step 7: Setup UFW Firewall
setup_firewall() {
    log "Step 7: Configuring UFW firewall"
    
    # Install UFW
    apt install -y ufw
    
    # Set default policies
    ufw default deny incoming
    ufw default allow outgoing
    
    # Allow essential ports
    ufw allow 80/tcp comment 'HTTP'
    ufw allow 443/tcp comment 'HTTPS'
    ufw allow ${SSH_PORT}/tcp comment 'SSH on custom port'
    ufw allow from 10.0.0.0/8 to any port 5432 comment 'PostgreSQL local network'
    
    # Enable firewall
    ufw --force enable
    
    log "UFW firewall configured"
}

# Step 8: Advanced SSH Hardening
setup_ssh_hardening() {
    log "Step 8: Hardening SSH configuration"

    # Backup original SSH config
    cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

    # Create hardened SSH configuration
    cat > /etc/ssh/sshd_config << EOF
# Advanced SSH Security Configuration for Fasgo VPS
Port ${SSH_PORT}
Protocol 2

# Authentication
PermitRootLogin yes
PubkeyAuthentication yes
PasswordAuthentication no
PermitEmptyPasswords no
ChallengeResponseAuthentication no
UsePAM yes

# Security Settings
MaxAuthTries 3
MaxSessions 2
LoginGraceTime 30
ClientAliveInterval 300
ClientAliveCountMax 2

# Disable dangerous features
X11Forwarding no
AllowTcpForwarding no
GatewayPorts no
PermitTunnel no
AllowAgentForwarding no

# Logging
SyslogFacility AUTH
LogLevel VERBOSE

# Crypto
Ciphers <EMAIL>,<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr
MACs <EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha2-512
KexAlgorithms <EMAIL>,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512

# Banner
Banner /etc/ssh/banner

# Subsystem
Subsystem sftp /usr/lib/openssh/sftp-server -l INFO

# Allow specific users only
AllowUsers root
EOF

    # Create SSH banner
    cat > /etc/ssh/banner << 'EOF'
***************************************************************************
                        FASGO VPS - AUTHORIZED ACCESS ONLY
***************************************************************************

WARNING: This system is for authorized users only. All activities are
monitored and logged. Unauthorized access is strictly prohibited and
will be prosecuted to the full extent of the law.

By accessing this system, you agree to comply with all applicable
security policies and procedures.

***************************************************************************
EOF

    log "SSH hardening completed"
}

# Step 9: Install and Configure Fail2Ban
setup_fail2ban() {
    log "Step 9: Installing and configuring Fail2Ban"

    apt install -y fail2ban

    # Create Fail2Ban configuration
    cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
# Ban settings
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

# Email notifications
destemail = ${ADMIN_EMAIL}
sendername = Fail2Ban-Fasgo
mta = sendmail

# SSH Protection
[sshd]
enabled = true
port = ssh,${SSH_PORT}
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

# Nginx Protection
[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
logpath = /var/log/nginx/access.log
maxretry = 2

# PHP Protection
[php-url-fopen]
enabled = true
filter = php-url-fopen
logpath = /var/log/nginx/access.log
maxretry = 1

# PostgreSQL Protection
[postgresql]
enabled = true
filter = postgresql
logpath = /var/log/postgresql/postgresql-*.log
maxretry = 3
EOF

    # Enable and start Fail2Ban
    systemctl enable fail2ban
    systemctl start fail2ban

    log "Fail2Ban installation completed"
}

# Step 10: Install Security Tools
setup_security_tools() {
    log "Step 10: Installing security tools"

    # Install ClamAV antivirus
    apt install -y clamav clamav-daemon clamav-freshclam

    # Install additional security tools
    apt install -y rkhunter chkrootkit lynis unattended-upgrades apt-listchanges

    # Enable ClamAV
    systemctl enable clamav-freshclam
    systemctl start clamav-freshclam

    log "Security tools installation completed"
}

# Step 11: Kernel Hardening
setup_kernel_hardening() {
    log "Step 11: Applying kernel security hardening"

    cat > /etc/sysctl.d/99-fasgo-security.conf << 'EOF'
# Fasgo VPS Security Hardening - Kernel Parameters

# Network Security
net.ipv4.ip_forward = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv6.conf.all.accept_redirects = 0
net.ipv6.conf.default.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv6.conf.all.accept_source_route = 0
net.ipv6.conf.default.accept_source_route = 0

# Enable IP spoofing protection
net.ipv4.conf.all.rp_filter = 1
net.ipv4.conf.default.rp_filter = 1

# Log suspicious packets
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1

# Ignore ping requests
net.ipv4.icmp_echo_ignore_all = 1

# Ignore broadcast ping requests
net.ipv4.icmp_echo_ignore_broadcasts = 1

# Disable IPv6 if not needed
net.ipv6.conf.all.disable_ipv6 = 1
net.ipv6.conf.default.disable_ipv6 = 1

# TCP hardening
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_max_syn_backlog = 2048
net.ipv4.tcp_synack_retries = 2
net.ipv4.tcp_syn_retries = 5

# Memory protection
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
kernel.yama.ptrace_scope = 1

# File system hardening
fs.suid_dumpable = 0
fs.protected_hardlinks = 1
fs.protected_symlinks = 1

# Process restrictions
kernel.core_uses_pid = 1
kernel.ctrl-alt-del = 0
EOF

    # Apply kernel parameters
    sysctl -p /etc/sysctl.d/99-fasgo-security.conf

    log "Kernel hardening completed"
}

# Step 12: PHP Security Configuration
setup_php_security() {
    log "Step 12: Securing PHP configuration"

    # Backup original PHP configuration
    cp /etc/php/8.2/fpm/php.ini /etc/php/8.2/fpm/php.ini.backup

    # Create PHP security configuration
    cat > /etc/php/8.2/fpm/conf.d/99-fasgo-security.ini << 'EOF'
; Fasgo VPS PHP Security Configuration

; Disable dangerous functions
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source,file_get_contents,fopen,fwrite,fputs,fgets,fgetcsv,fgetss,file,readfile,highlight_file

; Hide PHP version
expose_php = Off

; Disable remote file inclusion
allow_url_fopen = Off
allow_url_include = Off

; Session security
session.cookie_httponly = On
session.cookie_secure = On
session.use_strict_mode = On
session.cookie_samesite = "Strict"

; File upload restrictions
file_uploads = On
upload_max_filesize = 10M
max_file_uploads = 5

; Memory and execution limits
memory_limit = 256M
max_execution_time = 30
max_input_time = 30

; Error handling
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; SQL injection protection
magic_quotes_gpc = Off
magic_quotes_runtime = Off

; XSS protection
default_charset = "UTF-8"
EOF

    log "PHP security configuration completed"
}

# Step 13: Create Security Monitoring Scripts
setup_monitoring() {
    log "Step 13: Setting up security monitoring"

    # Create security directory
    mkdir -p /opt/fasgo-security

    # Create security scan script
    cat > /opt/fasgo-security/security-scan.sh << 'EOF'
#!/bin/bash
# Fasgo VPS Security Scanner
# Daily security check script

LOG_FILE="/var/log/fasgo-security.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "[$DATE] Starting Fasgo VPS Security Scan" >> $LOG_FILE

# 1. Run ClamAV scan
echo "[$DATE] Running ClamAV scan..." >> $LOG_FILE
clamscan -r /var/www/ --log=$LOG_FILE --infected --remove=yes

# 2. Run RKHunter
echo "[$DATE] Running RKHunter scan..." >> $LOG_FILE
rkhunter --check --skip-keypress --report-warnings-only >> $LOG_FILE

# 3. Run ChkRootkit
echo "[$DATE] Running ChkRootkit scan..." >> $LOG_FILE
chkrootkit >> $LOG_FILE

# 4. Check for failed login attempts
echo "[$DATE] Checking failed login attempts..." >> $LOG_FILE
grep "Failed password" /var/log/auth.log | tail -10 >> $LOG_FILE

# 5. Check disk usage
echo "[$DATE] Checking disk usage..." >> $LOG_FILE
df -h >> $LOG_FILE

# 6. Check memory usage
echo "[$DATE] Checking memory usage..." >> $LOG_FILE
free -h >> $LOG_FILE

# 7. Check running processes
echo "[$DATE] Checking suspicious processes..." >> $LOG_FILE
ps aux --sort=-%cpu | head -10 >> $LOG_FILE

echo "[$DATE] Security scan completed" >> $LOG_FILE
echo "----------------------------------------" >> $LOG_FILE
EOF

    # Create intrusion detection script
    cat > /opt/fasgo-security/intrusion-monitor.sh << EOF
#!/bin/bash
# Fasgo VPS Intrusion Detection System

ALERT_EMAIL="${ADMIN_EMAIL}"
LOG_FILE="/var/log/fasgo-intrusion.log"
DATE=\$(date '+%Y-%m-%d %H:%M:%S')

# Function to send alert
send_alert() {
    local message="\$1"
    echo "[\$DATE] SECURITY ALERT: \$message" >> \$LOG_FILE
    echo "FASGO VPS SECURITY ALERT: \$message" | mail -s "Security Alert - VPS Intrusion Detected" \$ALERT_EMAIL 2>/dev/null
}

# Check for multiple failed SSH attempts
failed_ssh=\$(grep "Failed password" /var/log/auth.log | grep "\$(date '+%b %d')" | wc -l)
if [ \$failed_ssh -gt 10 ]; then
    send_alert "Multiple SSH login failures detected: \$failed_ssh attempts today"
fi

# Check for new users
new_users=\$(grep "new user" /var/log/auth.log | grep "\$(date '+%b %d')" | wc -l)
if [ \$new_users -gt 0 ]; then
    send_alert "New user account created: Check /var/log/auth.log"
fi

# Check for privilege escalation
sudo_attempts=\$(grep "sudo" /var/log/auth.log | grep "\$(date '+%b %d')" | wc -l)
if [ \$sudo_attempts -gt 20 ]; then
    send_alert "Excessive sudo attempts detected: \$sudo_attempts attempts today"
fi

# Check disk usage
disk_usage=\$(df / | awk 'NR==2 {print \$5}' | sed 's/%//')
if [ \$disk_usage -gt 90 ]; then
    send_alert "Disk usage critical: \${disk_usage}% full"
fi

echo "[\$DATE] Intrusion monitoring completed" >> \$LOG_FILE
EOF

    # Create security status dashboard
    cat > /opt/fasgo-security/security-status.sh << 'EOF'
#!/bin/bash
# Fasgo VPS Security Status Dashboard

echo "=========================================="
echo "    FASGO VPS SECURITY STATUS REPORT"
echo "=========================================="
echo "Generated: $(date)"
echo ""

echo "🔒 FIREWALL STATUS:"
ufw status | head -10
echo ""

echo "🛡️ FAIL2BAN STATUS:"
fail2ban-client status
echo ""

echo "🦠 ANTIVIRUS STATUS:"
systemctl is-active clamav-freshclam
echo "Last virus database update:"
stat /var/lib/clamav/daily.cvd 2>/dev/null | grep Modify || echo "Database not found"
echo ""

echo "🔍 RECENT SECURITY EVENTS:"
echo "Failed SSH attempts (last 24h):"
grep "Failed password" /var/log/auth.log | grep "$(date '+%b %d')" | wc -l
echo ""

echo "📊 SYSTEM RESOURCES:"
echo "CPU Usage:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
echo "Memory Usage:"
free | grep Mem | awk '{printf "%.1f%%\n", $3/$2 * 100.0}'
echo "Disk Usage:"
df -h / | awk 'NR==2 {print $5}'
echo ""

echo "✅ SECURITY SERVICES STATUS:"
echo "SSH: $(systemctl is-active ssh)"
echo "Nginx: $(systemctl is-active nginx)"
echo "PHP-FPM: $(systemctl is-active php8.2-fpm)"
echo "PostgreSQL: $(systemctl is-active postgresql)"
echo "Fail2Ban: $(systemctl is-active fail2ban)"
echo "ClamAV: $(systemctl is-active clamav-freshclam)"
echo ""

echo "=========================================="
echo "    END OF SECURITY STATUS REPORT"
echo "=========================================="
EOF

    # Make scripts executable
    chmod +x /opt/fasgo-security/*.sh

    log "Security monitoring scripts created"
}

# Step 14: Setup Automated Tasks
setup_automation() {
    log "Step 14: Setting up automated security tasks"

    # Create cron jobs for security tasks
    cat > /tmp/fasgo_cron << EOF
# Fasgo VPS Security Tasks
0 2 * * * /opt/fasgo-security/security-scan.sh
0 3 * * 0 /usr/bin/rkhunter --update
0 4 * * 0 /usr/bin/freshclam
30 1 * * * /usr/bin/apt update && /usr/bin/apt upgrade -y
0 0 * * * /usr/bin/find /var/log -name "*.log" -mtime +30 -delete
*/15 * * * * /opt/fasgo-security/intrusion-monitor.sh
EOF

    # Install cron jobs
    crontab /tmp/fasgo_cron
    rm /tmp/fasgo_cron

    log "Automated tasks configured"
}

# Step 15: Create Website Structure
setup_website() {
    log "Step 15: Setting up website structure"

    # Create website directories
    mkdir -p /var/www/html
    mkdir -p /var/www/laravel
    mkdir -p /var/www/wordpress

    # Set proper permissions
    chown -R www-data:www-data /var/www/
    chmod -R 755 /var/www/

    # Create a beautiful landing page
    cat > /var/www/html/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fasgo VPS - Setup Complete</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-color: #2d3748;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            padding: 2rem;
            text-align: center;
        }

        .hero {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .status-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            padding: 1.5rem;
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .status-title {
            font-weight: 600;
            color: white;
            margin-bottom: 0.5rem;
        }

        .status-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .success {
            color: #48bb78;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <div class="logo">FASGO VPS</div>
            <div class="subtitle">Production-Ready Server Setup Complete</div>
            <p style="color: rgba(255, 255, 255, 0.8);">
                Your VPS is now configured with enterprise-grade security and performance optimizations.
            </p>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <div class="status-icon success">🔒</div>
                <div class="status-title">Security Hardened</div>
                <div class="status-desc">Advanced firewall, intrusion detection, and monitoring active</div>
            </div>
            <div class="status-card">
                <div class="status-icon success">🌐</div>
                <div class="status-title">Web Server Ready</div>
                <div class="status-desc">Nginx with SSL certificates and security headers</div>
            </div>
            <div class="status-card">
                <div class="status-icon success">🐘</div>
                <div class="status-title">Database Active</div>
                <div class="status-desc">PostgreSQL configured and optimized</div>
            </div>
            <div class="status-card">
                <div class="status-icon success">⚡</div>
                <div class="status-title">PHP 8.2 Ready</div>
                <div class="status-desc">Latest PHP with security configurations</div>
            </div>
        </div>
    </div>
</body>
</html>
EOF

    # Create PHP info page
    cat > /var/www/html/info.php << 'EOF'
<?php
phpinfo();
?>
EOF

    log "Website structure created"
}

# Step 16: Final Configuration and Restart Services
finalize_setup() {
    log "Step 16: Finalizing setup and restarting services"

    # Restart all services with new configurations
    systemctl restart php8.2-fpm
    systemctl restart nginx
    systemctl restart fail2ban

    # Update UFW rules for new SSH port (remove old port 22)
    ufw delete allow 22 2>/dev/null || true

    # Generate final report
    cat > /root/setup_report.txt << EOF
=================================================
    FASGO VPS SETUP COMPLETION REPORT
=================================================
Date: $(date)
Server IP: $(curl -s ifconfig.me 2>/dev/null || echo "Unable to detect")

DOMAIN CONFIGURATION:
- Domain: ${DOMAIN_NAME}
- SSL Certificate: Installed and configured
- Admin Email: ${ADMIN_EMAIL}

DATABASE CREDENTIALS:
- Database Name: ${DB_NAME}
- Database User: ${DB_USER}
- Database Password: ${DB_PASSWORD}

SECURITY CONFIGURATION:
- SSH Port: ${SSH_PORT}
- Firewall: UFW enabled with restricted access
- Fail2Ban: Active with email notifications
- ClamAV: Antivirus scanning enabled
- Intrusion Detection: Active monitoring every 15 minutes

SERVICES STATUS:
- Nginx: $(systemctl is-active nginx)
- PHP-FPM: $(systemctl is-active php8.2-fpm)
- PostgreSQL: $(systemctl is-active postgresql)
- Fail2Ban: $(systemctl is-active fail2ban)
- ClamAV: $(systemctl is-active clamav-freshclam)

IMPORTANT NOTES:
1. SSH port has been changed to ${SSH_PORT}
2. Password authentication is disabled - use SSH keys only
3. Database credentials are saved in /root/database_credentials.txt
4. Security monitoring scripts are in /opt/fasgo-security/
5. Run /opt/fasgo-security/security-status.sh for security status

NEXT STEPS:
1. Update your Cloudflare DNS to point to this server IP
2. Test website access: https://${DOMAIN_NAME}
3. Configure SSH key authentication before disconnecting
4. Review security logs regularly

=================================================
EOF

    chmod 600 /root/setup_report.txt

    log "Setup finalization completed"
}

# Main execution function
main() {
    check_root
    show_banner

    log "Starting complete VPS setup process..."

    # Basic setup
    setup_system
    setup_nginx
    setup_php
    setup_postgresql
    setup_composer
    setup_ssl
    setup_firewall

    # Security hardening
    setup_ssh_hardening
    setup_fail2ban
    setup_security_tools
    setup_kernel_hardening
    setup_php_security
    setup_monitoring
    setup_automation

    # Final configuration
    setup_website
    finalize_setup

    # Display completion message
    echo -e "${GREEN}"
    echo "=================================================="
    echo "    🎉 FASGO VPS SETUP COMPLETED SUCCESSFULLY! 🎉"
    echo "=================================================="
    echo -e "${NC}"
    echo ""
    echo -e "${BLUE}📋 Setup Report: ${NC}/root/setup_report.txt"
    echo -e "${BLUE}🔐 Database Credentials: ${NC}/root/database_credentials.txt"
    echo -e "${BLUE}🛡️ Security Status: ${NC}/opt/fasgo-security/security-status.sh"
    echo ""
    echo -e "${YELLOW}⚠️  IMPORTANT: ${NC}"
    echo "1. SSH port changed to ${SSH_PORT}"
    echo "2. Configure SSH keys before disconnecting"
    echo "3. Update Cloudflare DNS to point to this server"
    echo "4. Test website: https://${DOMAIN_NAME}"
    echo ""
    echo -e "${GREEN}✅ Your VPS is now production-ready with enterprise security!${NC}"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
